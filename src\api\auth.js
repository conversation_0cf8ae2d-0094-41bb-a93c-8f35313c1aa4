export const login = (data) =>
  request.Post(
    '/gateway',
    {
      apiCode: 'BA003',
      ...data,
    },
    {
      meta: {
        authRole: 'login',
      },
    },
  )

export const sendCode = (data) =>
  request.Post(
    '/gateway',
    {
      apiCode: 'BA002',
      ...data,
    },
    {
      meta: {
        authRole: null,
        raw: true,
      },
    },
  )

export const getUserInfo = (data) =>
  request.Post(
    '/gateway',
    {
      apiCode: 'SF003',
      ...data,
    },
    {
      meta: {},
    },
  )

export const logout = (data) =>
  request.Post(
    '/gateway',
    {
      apiCode: '',
      ...data,
    },
    {
      meta: {
        authRole: 'logout',
      },
    },
  )
