<template>
  <van-nav-bar v-bind="$attrs" @click-left="!custom && $router.back()">
    <template v-if="$slots.left" #left>
      <slot name="left"></slot>
    </template>
    <template v-if="$slots.title" #title>
      <slot name="title"></slot>
    </template>
    <template v-if="$slots.right" #right>
      <slot name="right"></slot>
    </template>
  </van-nav-bar>
</template>

<script setup>
const props = defineProps({
  custom: {
    type: Boolean,
    default: false,
  },
})
</script>

<style lang="scss" scoped></style>
