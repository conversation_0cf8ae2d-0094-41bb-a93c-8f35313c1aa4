<template>
  <Page class="">
    <template #header>
      <HomeHeader ref="navbar" />
    </template>
    <van-pull-refresh
      v-model="isLoading"
      :style="{
        backgroundPosition: `0 ${navbarHeight * -1}px`,
      }"
      @refresh="onRefresh()"
    >
      <AdBanner ref="adBanner1" regionType="HOME_BANNAR" class="m-2" />
      <AdBanner ref="adBanner2" regionType="HOME_BANNAR2" class="m-2" />
      <HomeQuickAccess ref="quickAccess" class="m-2" />
      <AdGrid regionType="HOME_MOFANG" class="m-2" />
      <AdKebab regionType="HOME_MIDDLE" class="my-2" />
      <HomeGoodsList />
      <!-- <ul>
        <li v-for="i in 1000">
          {{ i }}
        </li>
      </ul>
      <van-button to="/login"> Login </van-button>
      <van-button to="/goods-category"> 分类 </van-button> -->
    </van-pull-refresh>
    <!-- <template #header>
      </template> -->
  </Page>
</template>

<script setup>
defineOptions({
  name: 'Home',
})

const authStore = useAuthStore()
const navbar = useTemplateRef('navbar')
const adBanner1 = useTemplateRef('adBanner1')
const adBanner2 = useTemplateRef('adBanner2')
const quickAccess = useTemplateRef('quickAccess')

const { height: navbarHeight } = useElementSize(() => unrefElement(navbar))

// const loading = ref()
// function onRefresh() {}

const { isLoading, execute: onRefresh } = useAsyncState(
  async () => {
    // await new Promise((resolve) => setTimeout(resolve, 1000))
    await authStore.refresh()
    adBanner1.value?.refresh()
    adBanner2.value?.refresh()
    quickAccess.value?.refresh()
  },
  null,
  {
    immediate: false,
  },
)
</script>

<style lang="scss" scoped>
.page {
  // overflow-y: scroll;
  // :deep(){}
  // display: flex;
  // flex-direction: column;
  // background-image: linear-gradient(to bottom, #5581f6 0, #f4f6fa 606px);
  // background-repeat: no-repeat;
  :deep(header) {
    // flex: none;
    background-image: linear-gradient(to bottom, #5581f6 0, #f4f6fa 606px);
    background-repeat: no-repeat;
  }
  :deep(main) {
    // overflow-y: scroll;
    // flex: 1;
    // background-image: linear-gradient(to bottom, #5581f6 0, #f4f6fa 606px);
    // background-position: 0 -46px;
    // background-repeat: no-repeat;
  }
  .van-pull-refresh {
    background-image: linear-gradient(to bottom, #5581f6 0, #f4f6fa 606px);
    // background-position: 0 calc(-46px - env(safe-area-inset-bottom, 0px));
    background-repeat: no-repeat;
    --van-pull-refresh-head-text-color: #ccc;
    --van-loading-spinner-color: var(--van-pull-refresh-head-text-color);
    --van-loading-text-color: var(--van-loading-spinner-color);
    :deep(.van-pull-refresh__track) {
    }
  }
}
.van-nav-bar {
  &::after {
    // display: none;
  }
}

.ad-banner {
}
</style>
