/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/(app)': RouteRecordInfo<'/(app)', '/', Record<never, never>, Record<never, never>, '/(app)/' | '/(app)/goods-category' | '/(app)/login' | '/(app)/my/' | '/(app)/my/consume-quota'>,
    '/(app)/': RouteRecordInfo<'/(app)/', '/', Record<never, never>, Record<never, never>>,
    '/(app)/goods-category': RouteRecordInfo<'/(app)/goods-category', '/goods-category', Record<never, never>, Record<never, never>>,
    '/(app)/login': RouteRecordInfo<'/(app)/login', '/login', Record<never, never>, Record<never, never>>,
    '/(app)/my/': RouteRecordInfo<'/(app)/my/', '/my', Record<never, never>, Record<never, never>>,
    '/(app)/my/consume-quota': RouteRecordInfo<'/(app)/my/consume-quota', '/my/consume-quota', Record<never, never>, Record<never, never>>,
    '/(download).vue/download/': RouteRecordInfo<'/(download).vue/download/', '/(download)/vue/download', Record<never, never>, Record<never, never>>,
  }
}
