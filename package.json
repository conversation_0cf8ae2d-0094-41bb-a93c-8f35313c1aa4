{"name": "qxhua-app", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@vueuse/core": "^13.6.0", "@vueuse/router": "^13.6.0", "alova": "^3.3.4", "lodash-es": "^4.17.21", "motion-v": "^1.6.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "swiper": "^11.2.10", "ua-parser-js": "^2.0.4", "uid": "^2.0.2", "vant": "^4.9.21", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.31.0", "@iconify/json": "^2.2.364", "@iconify/utils": "^2.3.0", "@unocss/preset-icons": "^66.3.3", "@unocss/preset-wind4": "^66.3.3", "@unocss/transformer-directives": "^66.3.3", "@unocss/transformer-variant-group": "^66.3.3", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/eslint-config-prettier": "^10.2.0", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "globals": "^16.3.0", "postcss-px-to-viewport": "^1.1.1", "prettier": "3.6.2", "sass-embedded": "^1.89.2", "unocss": "^66.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "unplugin-vue-router": "^0.14.0", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0"}}