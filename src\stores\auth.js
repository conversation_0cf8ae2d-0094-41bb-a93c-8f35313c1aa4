import { getUserInfo } from '@/api/auth'
export const useAuthStore = defineStore(
  'auth',
  () => {
    const accessToken = ref('')
    const refreshToken = ref('')

    const {
      data: user,
      send: refresh,
      loading,
      error,
    } = useWatcher(() => getUserInfo(), [accessToken], {
      async middleware(_, next) {
        if (!accessToken.value) return
        // await new Promise((resolve) => setTimeout(resolve, 100))
        return next()
      },
    })

    return { accessToken, user, refreshToken, refresh, loading, error }
  },
  {
    persist: {
      pick: ['accessToken'],
      async afterHydrate(ctx) {},
    },
  },
)
