<template>
  <!-- <div> -->
  <!-- <section class="app"> -->
  <section class="app size-screen relative transform-3d z-0">
    <router-view v-slot="{ Component, route }">
      <!-- <keep-alive> -->
      <AnimatePresence mode="popLayout" :custom="direction">
        <Motion
          :key="route.path"
          :variants="variants"
          initial="initial"
          animate="animate"
          exit="exit"
          :transition="{
            // duration: 3,
            type: 'tween',
          }"
          class="size-screen page-wrapper"
          as="section"
        >
          <component
            :is="Component"
            class="size-full"
            :class="{
              'is-tabber': tabbarIsShow,
              '[&_main]:p-b-[var(--tabber-height)]': tabbarIsShow,
            }"
          ></component>
        </Motion>
      </AnimatePresence>
      <!-- </keep-alive> -->
    </router-view>
    <!-- </section> -->
    <!-- </div> -->
    <AnimatePresence>
      <Motion
        v-if="tabbarIsShow"
        :initial="{
          y: 50,
        }"
        :animate="{
          y: 0,
        }"
        :exit="{
          y: 50,
        }"
        :transition="{
          type: 'tween',
        }"
        as-child
      >
        <van-tabbar
          :model-value="currentActiveNavBarName"
          class="b-rounded-t-5 overflow-hidden shadow-xs z-10"
          @change="handleNavChange"
        >
          <van-tabbar-item v-for="item in navMenuList" :key="item.name" :name="item.name" class="">
            <span class="">
              {{ item.title }}
            </span>
            <template #icon="{ active }">
              <motion.img
                :key="`${item.name}-${active}`"
                :src="active ? item.iconSrcSelected : item.iconSrc"
                :alt="item.title"
                :initial="
                  active
                    ? {
                        scale: 0,
                      }
                    : false
                "
                :animate="{
                  scale: 1,
                }"
              />
            </template>
          </van-tabbar-item>
        </van-tabbar>
      </Motion>
    </AnimatePresence>
  </section>
</template>

<script setup>
import { getNavMenuList } from '@/api/app'
import { Motion, motion, AnimatePresence } from 'motion-v'
const router = useRouter()
const route = useRoute()

const appStore = useAppStore()
const userStore = useAuthStore()

const currentActiveNavBarName = computed(() => route.path)

// function getNavBarItemName(item) {
//   return router.resolve(item.url).path
// }

const { data: navMenuList } = useWatcher(() => getNavMenuList(), [() => appStore.channel], {
  immediate: true,
  initialData: [],
  async middleware(_, next) {
    const data = await next()
    data.forEach((v) => {
      v.name = v.url
    })
    return data
  },
})

function handleNavChange(name) {
  const item = navMenuList.value.find((v) => v.name === name)
  console.log(item)
  if (item.type === '1') {
    // 切换tab
    // active.value = name
    router.replace(item.url)
    return
  }
  if (item.type === '2') {
    location.assign(item.url)
    return
  }
  if (item.type === '4') {
    router.push(item.url)
    return
  }
}

const tabbarIsShow = computed(() => {
  const item = navMenuList.value.find((v) => v.name === route.path)
  if (!item) return false
  return item.type === '1'
})

const currentPosition = ref(history.state?.position ?? 0)

const direction = ref()

const variants = {
  initial(direction) {
    // console.log(direction)
    switch (direction) {
      case 'forward':
        return {
          x: '100%',
          zIndex: 1,
        }
      case 'back':
        return {
          x: '-50%',
          zIndex: 0,
        }
      case 'replace':
        return false
      default:
        return false
    }
  },
  animate(direction) {
    switch (direction) {
      case 'forward':
        return {
          x: '0%',
          zIndex: 1,
        }
      case 'back':
        return {
          x: '0%',
          zIndex: 1,
        }
      case 'replace':
        return false
      default:
        return false
    }
  },
  exit(direction) {
    switch (direction) {
      case 'forward':
        return {
          x: '-50%',
          zIndex: 0,
          opacity: 0,
        }
      case 'back':
        return {
          x: '100%',
          zIndex: 1,
          opacity: 0,
        }
      case 'replace':
        return false
      default:
        return false
    }
  },
}

onBeforeUnmount(
  router.afterEach((to, from) => {
    const position = history.state?.position ?? 0
    if (position > currentPosition.value) {
      direction.value = 'forward'
      currentPosition.value = position
      return
    }
    if (position < currentPosition.value) {
      direction.value = 'back'
      currentPosition.value = position
      return
    }
    direction.value = 'replace'
    currentPosition.value = position
    return
  }),
)
</script>

<style lang="scss" scoped>
.app {
  --tabber-height: calc(50px + env(safe-area-inset-bottom));
}
</style>
