export const useAppStore = defineStore('app', () => {
  const name = ref('轻享花')
  const authStore = useAuthStore()

  const { state: env } = useAsyncState(() => appJs.getEnv(), {})
  // const { state: channel } = useAsyncState(() => appJs.getAppChannel(), 'VERIFY')
  const { state: version } = useAsyncState(() => appJs.getAppVersion(), '0.0.0')

  const channel = computed(() => {
    if (authStore.user?.creditIntentionFlag === 'Y') {
      return 'OFFICIAL'
    }
    return 'VERIFY'
  })

  return { env, name, channel, version }
})
