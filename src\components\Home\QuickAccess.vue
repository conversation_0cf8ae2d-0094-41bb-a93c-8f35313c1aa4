<template>
  <div class="home-quick-access grid grid-cols-5 gap-2">
    <div v-for="item in data" class="">
      <img :src="item.icon" alt="" class="size-10 block m-auto" />
      <div class="text-center mt-1">
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { getFuncRegion } from '@/api/app'
const appStore = useAppStore()
const {
  data,
  send: refresh,
  loading,
} = useWatcher(
  () =>
    getFuncRegion({
      funcRegion: 'APP_HOME',
    }),
  [computed(() => appStore.channel)],
  {
    immediate: true,
  },
)
defineExpose({ refresh })
</script>

<style lang="scss" scoped></style>
