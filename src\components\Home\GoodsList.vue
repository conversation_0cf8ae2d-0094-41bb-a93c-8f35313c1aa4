<template>
  <div class="">
    <div>商品列表</div>
    <!-- {{ data }} -->

    <div class="">
      <div class=""></div>
    </div>
    <van-tabs v-model:active="active">
      <van-tab
        v-for="item in categories"
        :key="item.categoryId"
        :name="item.categoryId"
        :title="item.categoryName"
      >
      </van-tab>
    </van-tabs>
    <van-list
      v-model:loading="loading"
      :finished="isLastPage"
      finished-text="没有更多了"
      :immediate-check="false"
      @load="onLoad"
    >
      <GoodsList :data="data" />
      <!-- <div class="">
        <div class="" v-for="item in data" :key="item.spuId">
          {{ item.spuTitle }}
        </div>
      </div> -->
    </van-list>
  </div>
</template>

<script setup>
import { getGoodsCategories, getGoodsList } from '@/api/goods'
import { cloneDeep, groupBy } from 'lodash-es'
import GoodsList from '@/components/GoodsList.vue'

const active = ref()

const { data: categories } = useRequest(() => getGoodsCategories(), {
  initialData: [],
  async middleware(_, next) {
    const data = await next()
    const map = new Map()
    data.forEach((item) => {
      if (!map.has(item.categoryId)) {
        item.children = []
        map.set(item.categoryId, item)
      }
      if (!map.has(item.parentId)) {
        map.set(item.parentId, {
          categoryId: item.parentId,
          children: [],
        })
      }
      const parent = map.get(item.parentId)
      parent.children.push(item)
    })

    return map.get(0)?.children ?? []
  },
})

const { data, reload, page, isLastPage, loading, total } = usePagination(
  (pageNum, pageSize) =>
    getGoodsList({
      pageNum,
      pageSize,
      categoryId: active.value,
    }),
  {
    append: true,
    watchingStates: [active],
  },
)

function onLoad() {
  page.value++
}
</script>

<style lang="scss" scoped></style>
