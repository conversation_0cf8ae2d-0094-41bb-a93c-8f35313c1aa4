<template>
  <div class="ad-grid grid grid-cols-2 gap-y-2">
    <div v-for="item in data" class="item">
      <img :src="item.pic" alt="" class="block" />
    </div>
  </div>
</template>

<script setup>
import { getAdInfo } from '@/api/app'

const props = defineProps({
  regionType: {
    type: String,
    default: '',
  },
})

const appStore = useAppStore()
const authStore = useAuthStore()

const {
  data,
  send: refresh,
  loading,
} = useWatcher(
  () =>
    getAdInfo({
      regionType: [props.regionType],
      // appVersion: appStore.channel,
    }),
  [() => props.regionType, computed(() => appStore.channel)],
  {
    initialData: [],
    immediate: true,
    async middleware(_, next) {
      const data = await next()
      return data?.[props.regionType] ?? []
    },
    // debounce: 0,
    // force: true,
    // shareRequest: false,
  },
)

defineExpose({ refresh })
</script>

<style lang="scss" scoped></style>
