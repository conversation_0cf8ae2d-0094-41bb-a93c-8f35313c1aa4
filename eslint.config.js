import { defineConfig, globalIgnores } from 'eslint/config'
import globals from 'globals'
import js from '@eslint/js'
import pluginVue from 'eslint-plugin-vue'
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'
import antfu from '@antfu/eslint-config'
import { FlatCompat } from '@eslint/eslintrc'
// export default defineConfig([
//   {
//     name: 'app/files-to-lint',
//     files: ['**/*.{js,mjs,jsx,vue}'],
//   },

//   globalIgnores(['**/dist/**', '**/dist-ssr/**', '**/coverage/**']),

//   {
//     languageOptions: {
//       globals: {
//         ...globals.browser,
//       },
//     },
//   },

//   js.configs.recommended,
//   ...pluginVue.configs['flat/essential'],
//   skipFormatting,
//   {
//     extends: ['./.eslintrc-auto-import.json'],
//   },
// ])

export default antfu(
  {
    vue: true,
    typescript: false,
    formatters: {
      css: true,
      html: true,
    },
    unocss: true,
    rules: {},
  },
  ...compat.config({
    extends: ['./.eslintrc-auto-import.json'],
    globals: {
      definePage: 'readonly',
    },
  }),
)
