<template>
  <Page>
    <template #header>
      <nav-bar title="登录" />
    </template>

    <van-form ref="formRef" @submit="submit()">
      <van-cell-group inset>
        <van-field
          v-model="form.phone"
          name="phone"
          type="text"
          label="手机号"
          placeholder="手机号"
          :rules="[
            { required: true, message: '请填写手机号' },
            { pattern: /^1[3456789]\d{9}$/, message: '请填写正确的手机号码' },
          ]"
        />
        <van-field
          v-model="form.code"
          type="text"
          name="code"
          label="验证码"
          placeholder="验证码"
          :rules="[{ required: true, message: '请填写验证码' }]"
        >
          <template #button>
            <van-button
              size="small"
              type="primary"
              :loading="sendingCode"
              :disabled="remaining > 0"
              class="send-code-btn"
              @click="sendCode()"
            >
              <span v-if="remaining > 0"> {{ remaining }}s </span>
              <span v-else>发送验证码</span>
            </van-button>
          </template>
        </van-field>
      </van-cell-group>
      <div style="margin: 16px">
        <van-button :loading="submitting" round block type="primary" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>
  </Page>
</template>

<script setup>
import { login, sendCode as sendCodeApi } from '@/api/auth'
const router = useRouter()
const {
  form,
  send: submit,
  loading: submitting,
} = useForm((data) => login(data), {
  initialForm: {
    phone: '17603013019',
    code: '',
  },
}).onSuccess(() => {
  router.replace('/')
})

const { remaining, start } = useCountdown(0)

const { send: sendCode, loading: sendingCode } = useRequest(
  () =>
    sendCodeApi({
      phone: form.value.phone,
    }),
  {
    immediate: false,
  },
).onSuccess(({ data }) => {
  start(60)
  if (data.code) {
    form.value.code = data.code
  }
})
</script>

<style lang="scss" scoped></style>
