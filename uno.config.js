import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders'

import { defineConfig } from 'unocss'

import presetWind4 from '@unocss/preset-wind4'
import transformerVariantGroup from '@unocss/transformer-variant-group'
import transformerDirectives from '@unocss/transformer-directives'
import presetIcons from '@unocss/preset-icons'

export default defineConfig({
  presets: [
    // presetWind3(),
    presetWind4({
      preflights: {
        reset: true,
      },
    }),
    presetIcons({
      collections: {
        custom: FileSystemIconLoader('./src/assets/icons'),
      },
      /* options */
    }),
    // ...custom presets
  ],
  transformers: [transformerDirectives(), transformerVariantGroup()],
  theme: {
    colors: {
      // 这里颜色值只是个默认值，真实的值在index.scss里面覆写。
      primary: 'red',
    },
  },
})
